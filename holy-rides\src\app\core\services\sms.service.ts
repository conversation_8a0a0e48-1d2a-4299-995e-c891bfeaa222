import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { UserService } from './user.service';
import { Ride } from '../models/ride.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class SmsService {
  private supabase: SupabaseClient;

  constructor(
    private userService: UserService,
    private authService: AuthService
  ) {
    this.supabase = authService.supabase;
  }

  /**
   * Send an SMS notification to a user via Supabase Twilio lambda function
   * @param to Phone number to send the SMS to
   * @param body Message body
   * @returns Promise resolving to the message SID if successful
   */
  async sendSms(to: string, body: string): Promise<string> {
    try {
      // Format phone number if needed (ensure it has the + prefix)

      const formattedPhone = this.formatPhoneNumber(to);

      // Call the Supabase lambda function to send the SMS
      const { data, error } = await this.supabase.functions.invoke('twilio', {
        body: {
          to: formattedPhone,
          message: body,
          from: "+17272025413"
        }
      });

      if (error) {
        console.error('Error calling Twilio lambda function:', error);
        throw error;
      }

      if (!data || !data.sid) {
        throw new Error('No message SID returned from Twilio lambda function');
      }

      console.log(`SMS sent successfully to ${to}, SID: ${data.sid}`);
      return data.sid;
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw error;
    }
  }

  /**
   * Send ride assignment notifications to both rider and driver
   * @param ride The ride that was assigned
   * @param driverId The ID of the driver assigned to the ride
   */
  async sendRideAssignmentNotifications(ride: Ride, driverId: string): Promise<void> {
    try {
      // Get rider and driver information
      const [rider, driver] = await Promise.all([
        this.userService.getUserById(ride.rider_id),
        this.userService.getUserById(driverId)
      ]);

      if (!rider || !driver) {
        throw new Error('Could not find rider or driver information');
      }

      // Check if phone numbers are available
      if (!rider.phone || !driver.phone) {
        console.warn('Phone number missing for rider or driver. SMS notification skipped.');
        return;
      }

      // Format pickup time for better readability
      const pickupTime = new Date(ride.pickup_time).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Send enhanced notification to rider
      await this.sendEnhancedDriverAssignedNotification(ride, driver);

      // Send notification to driver with more detailed information
      const driverMessage = `You have been assigned a new ride. Pick up ${rider.full_name || 'your rider'} at ${ride.pickup_location} at ${pickupTime} and drop off at ${ride.dropoff_location}. Rider phone: ${rider.phone}`;

      // Send driver notification
      const results = await Promise.allSettled([
        this.sendSmsWithRetry(driver.phone, driverMessage)
      ]);

      // Check results and log any failures
      const [driverResult] = results;

      if (driverResult.status === 'rejected') {
        console.error('Failed to send SMS to driver:', driverResult.reason);
      } else {
        console.log('Ride assignment notification sent successfully to driver');
      }
    } catch (error) {
      console.error('Error sending ride assignment notifications:', error);
      // Don't throw the error - we don't want to break the ride assignment process
      // if SMS sending fails
    }
  }

  /**
   * Send SMS with retry logic for better reliability
   * @param to Phone number to send the SMS to
   * @param body Message body
   * @param maxRetries Maximum number of retry attempts
   * @returns Promise resolving to the message SID if successful
   */
  private async sendSmsWithRetry(to: string, body: string, maxRetries = 2): Promise<string> {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Wait a bit before retrying (exponential backoff)
        if (attempt > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));
        }

        return await this.sendSms("+1"+to, body);
      } catch (error) {
        lastError = error;
        console.warn(`SMS sending attempt ${attempt + 1}/${maxRetries + 1} failed:`, error);
      }
    }

    // If we get here, all attempts failed
    throw lastError || new Error('Failed to send SMS after multiple attempts');
  }

  /**
   * Send ride status update notifications to both rider and driver
   * @param ride The ride that had its status updated
   * @param newStatus The new status of the ride
   */
  async sendRideStatusUpdateNotifications(ride: Ride, newStatus: string): Promise<void> {
    try {
      // Skip if the ride doesn't have both rider and driver assigned
      if (!ride.rider_id || !ride.driver_id) {
        console.warn('Ride is missing rider or driver ID. Status update notification skipped.');
        return;
      }

      // Get rider and driver information
      const [rider, driver] = await Promise.all([
        this.userService.getUserById(ride.rider_id),
        this.userService.getUserById(ride.driver_id)
      ]);

      if (!rider || !driver) {
        throw new Error('Could not find rider or driver information');
      }

      // Check if phone numbers are available
      if (!rider.phone || !driver.phone) {
        console.warn('Phone number missing for rider or driver. Status update notification skipped.');
        return;
      }

      // Create appropriate messages based on the new status
      let riderMessage = '';
      let driverMessage = '';

      switch (newStatus) {
        case 'in-progress':
          riderMessage = `Your ride has started. Your driver ${driver.full_name || 'is'} on the way to ${ride.dropoff_location}.`;
          driverMessage = `You have started the ride with ${rider.full_name || 'your rider'}. Destination: ${ride.dropoff_location}.`;
          break;
        case 'completed':
          riderMessage = `Your ride to ${ride.dropoff_location} has been completed. Thank you for using Holy Rides!`;
          driverMessage = `You have completed the ride to ${ride.dropoff_location}. Thank you for your service!`;
          break;
        case 'canceled':
          riderMessage = `Your ride has been canceled. Please contact support if you did not request this cancellation.`;
          driverMessage = `The ride to ${ride.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;
          break;
        default:
          // Don't send notifications for other status changes
          return;
      }

      // Send both messages in parallel
      const results = await Promise.allSettled([
        this.sendSmsWithRetry(rider.phone, riderMessage),
        this.sendSmsWithRetry(driver.phone, driverMessage)
      ]);

      // Check results and log any failures
      const [riderResult, driverResult] = results;

      if (riderResult.status === 'rejected') {
        console.error('Failed to send status update SMS to rider:', riderResult.reason);
      }

      if (driverResult.status === 'rejected') {
        console.error('Failed to send status update SMS to driver:', driverResult.reason);
      }

      if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {
        console.log(`Ride status update (${newStatus}) notifications sent successfully to both rider and driver`);
      }
    } catch (error) {
      console.error('Error sending ride status update notifications:', error);
      // Don't throw the error - we don't want to break the status update process
      // if SMS sending fails
    }
  }

  /**
   * Send ride booking confirmation to the rider
   * @param ride The ride that was booked
   */
  async sendRideBookingConfirmation(ride: Ride): Promise<void> {
    try {
      // Get rider information
      const rider = await this.userService.getUserById(ride.rider_id);

      if (!rider || !rider.phone) {
        console.warn('Rider not found or phone number missing. Booking confirmation SMS skipped.');
        return;
      }

      // Format pickup time
      const pickupDate = new Date(ride.pickup_time);
      const isASAP = pickupDate.getTime() <= Date.now() + (30 * 60 * 1000); // Within 30 minutes
      const pickupTimeStr = isASAP ? 'ASAP' : pickupDate.toLocaleString();
      const pickupDateStr = pickupDate.toLocaleDateString();

      // Format fare estimate
      const fareStr = ride.fare ? `$${ride.fare.toFixed(2)}` : 'TBD';

      const message = `Your ride has been booked!
Pickup: ${ride.pickup_location}
Dropoff: ${ride.dropoff_location}
Pick up date: ${pickupDateStr}
Pickup Time: ${pickupTimeStr}
Fare Estimate: ${fareStr}
Driver will be assigned shortly. You'll receive updates as your ride details are confirmed.
Msg & data rates may apply. Message frequency varies.
Reply STOP to unsubscribe. View our policy at: https://bookholyrides.com/?p=1163`;

      await this.sendSmsWithRetry(rider.phone, message);
      console.log('Ride booking confirmation sent to rider');
    } catch (error) {
      console.error('Error sending ride booking confirmation:', error);
      // Don't throw - we don't want to break ride creation if SMS fails
    }
  }

  /**
   * Send general ride booking notification to all drivers
   * @param ride The ride that was booked
   */
  async sendRideBookingNotificationToDrivers(ride: Ride): Promise<void> {
    try {
      // Get all approved drivers
      const drivers = await this.userService.getUsersByRole('driver');
      const approvedDrivers = drivers.filter(driver => driver.is_approved && driver.phone);

      if (approvedDrivers.length === 0) {
        console.warn('No approved drivers with phone numbers found. Driver notification skipped.');
        return;
      }

      const message = `A new trip has been booked! Login now at https://app.bookholyrides.com to view the ride details in the Holy Rides app.`;

      // Send to all drivers in parallel
      const results = await Promise.allSettled(
        approvedDrivers.map(driver => this.sendSmsWithRetry(driver.phone!, message))
      );

      // Log results
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`Ride booking notification sent to ${successful} drivers, ${failed} failed`);
    } catch (error) {
      console.error('Error sending ride booking notifications to drivers:', error);
      // Don't throw - we don't want to break ride creation if SMS fails
    }
  }

  /**
   * Send enhanced driver assigned notification to rider
   * @param ride The ride with assigned driver
   * @param driver The assigned driver information
   */
  async sendEnhancedDriverAssignedNotification(ride: Ride, driver: any): Promise<void> {
    try {
      // Get rider information
      const rider = await this.userService.getUserById(ride.rider_id);

      if (!rider || !rider.phone) {
        console.warn('Rider not found or phone number missing. Driver assigned notification skipped.');
        return;
      }

      // Note: Vehicle info and license number would need to be added to driver profile
      // For now, using placeholder values
      const vehicleInfo = driver.vehicle_info || 'Vehicle details will be provided';
      const licenseNumber = driver.license_number || 'License details will be provided';
      const eta = 'ETA will be calculated'; // This would need real-time calculation

      const message = `Great news — a driver has been assigned to your trip! 🚘
Driver Name: ${driver.full_name || 'Driver'}
Vehicle: ${vehicleInfo}
License Number: ${licenseNumber}
ETA: ${eta}
Msg & data rates may apply. Reply STOP to unsubscribe.`;

      await this.sendSmsWithRetry(rider.phone, message);
      console.log('Enhanced driver assigned notification sent to rider');
    } catch (error) {
      console.error('Error sending enhanced driver assigned notification:', error);
      // Don't throw - we don't want to break ride assignment if SMS fails
    }
  }

  /**
   * Send ride cancellation notifications
   * @param ride The cancelled ride
   */
  async sendRideCancellationNotifications(ride: Ride): Promise<void> {
    try {
      const notifications: Promise<string>[] = [];

      // Send to rider
      const rider = await this.userService.getUserById(ride.rider_id);
      if (rider && rider.phone) {
        const riderMessage = `Your ride from ${ride.pickup_location} to ${ride.dropoff_location} has been cancelled. You can book a new ride at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;
        notifications.push(this.sendSmsWithRetry(rider.phone, riderMessage));
      }

      // Send to assigned driver if any
      if (ride.driver_id) {
        const driver = await this.userService.getUserById(ride.driver_id);
        if (driver && driver.phone) {
          const driverMessage = `The ride to ${ride.dropoff_location} has been cancelled. Please check your dashboard for new ride opportunities at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;
          notifications.push(this.sendSmsWithRetry(driver.phone, driverMessage));
        }
      }

      // Send all notifications in parallel
      if (notifications.length > 0) {
        const results = await Promise.allSettled(notifications);
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        console.log(`Ride cancellation notifications: ${successful} sent, ${failed} failed`);
      }
    } catch (error) {
      console.error('Error sending ride cancellation notifications:', error);
      // Don't throw - we don't want to break ride cancellation if SMS fails
    }
  }

  /**
   * Send ride completion notification to rider with payment instructions
   * @param ride The completed ride
   */
  async sendRideCompletionNotification(ride: Ride): Promise<void> {
    try {
      // Get rider information
      const rider = await this.userService.getUserById(ride.rider_id);

      if (!rider || !rider.phone) {
        console.warn('Rider not found or phone number missing. Ride completion notification skipped.');
        return;
      }

      // Format fare amount
      const fareAmount = ride.fare ? `$${ride.fare.toFixed(2)}` : '$0.00';

      const message = `Your ride is complete! 🚘
Thanks for riding with Holy Rides. Please complete payment now in the amount of ${fareAmount} using one of the following methods:
Cash app: https://cash.app/$HolyRides24
Square: https://square.link/u/o9zzuiAv?src=sheet
Msg & data rates may apply. Reply STOP to unsubscribe.`;

      await this.sendSmsWithRetry(rider.phone, message);
      console.log('Ride completion notification sent to rider');
    } catch (error) {
      console.error('Error sending ride completion notification:', error);
      // Don't throw - we don't want to break ride completion if SMS fails
    }
  }

  /**
   * Send payment confirmation notification to rider
   * @param ride The ride that was paid for
   * @param paymentAmount The amount that was paid
   */
  async sendPaymentConfirmationNotification(ride: Ride, paymentAmount: number): Promise<void> {
    try {
      // Get rider information
      const rider = await this.userService.getUserById(ride.rider_id);

      if (!rider || !rider.phone) {
        console.warn('Rider not found or phone number missing. Payment confirmation notification skipped.');
        return;
      }

      // Format pickup time
      const pickupDate = new Date(ride.pickup_time);
      const isASAP = pickupDate.getTime() <= Date.now() + (30 * 60 * 1000); // Within 30 minutes
      const pickupTimeStr = isASAP ? 'ASAP' : pickupDate.toLocaleString();
      const pickupDateStr = pickupDate.toLocaleDateString();

      // Format payment amount
      const amountStr = `$${paymentAmount.toFixed(2)}`;

      const message = `Thank you! 🎉
We've received your payment of ${amountStr} for your recent ride.
Pickup: ${ride.pickup_location}
Dropoff: ${ride.dropoff_location}
Pick up date: ${pickupDateStr}
Pickup Time: ${pickupTimeStr}

Thanks for riding with Holy Rides. See you next time!
Msg & data rates may apply. Reply STOP to unsubscribe.`;

      await this.sendSmsWithRetry(rider.phone, message);
      console.log('Payment confirmation notification sent to rider');
    } catch (error) {
      console.error('Error sending payment confirmation notification:', error);
      // Don't throw - we don't want to break payment processing if SMS fails
    }
  }

  /**
   * Format phone number to ensure it has the international format with + prefix
   * @param phoneNumber The phone number to format
   * @returns Formatted phone number
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // If the phone number already starts with +, return it as is
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }

    // If it starts with a country code without +, add the +
    if (phoneNumber.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)) {
      return `+${phoneNumber}`;
    }

    // Otherwise, assume it's a US number without country code
    // and add +1 (you can modify this based on your target country)
    return `+1${phoneNumber.replace(/\D/g, '')}`;
  }
}
