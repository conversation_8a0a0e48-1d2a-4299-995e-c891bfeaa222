import { Component, OnInit, On<PERSON><PERSON><PERSON>, signal, ViewChild, AfterViewInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RideService } from '../../../../core/services/ride.service';
import { AuthService } from '../../../../core/services/auth.service';
import { Ride, RideStatus } from '../../../../core/models/ride.model';
import { User } from '../../../../core/models/user.model';
import { RideNavigationComponent } from '../ride-navigation/ride-navigation.component';
import { MessageService } from '../../../../core/services/message.service';
import { RideDetailComponent } from '../../../../shared/components/ride-detail/ride-detail.component';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-ride-assignments',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatTableModule,
    MatChipsModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatBadgeModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    RideNavigationComponent,
    RideDetailComponent,
    MatSortModule,
    MatPaginatorModule
  ],
  template: `
    <div class="assignments-container">
      <mat-tab-group>
        <mat-tab label="Available Rides" [tabIndex]="0">
          <div class="table-container">
            <div class="header-with-actions">
              <h3>Available Ride Requests <span class="realtime-indicator" title="Realtime updates active">●</span></h3>
              <button mat-icon-button color="primary" (click)="loadRides()" matTooltip="Manual refresh">
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
            <div *ngIf="loading" class="loading-container">
              <mat-spinner diameter="40"></mat-spinner>
              <p>Loading rides...</p>
            </div> 
            
            <div *ngIf="!loading && availableRides().length === 0" class="no-rides">
              <p>No available ride requests at this time.</p>
            </div>

            <table mat-table [dataSource]="availableDataSource"  #availableSort="matSort"  matSort class="ride-table" *ngIf="!loading || availableRides().length > 0 ">
              <ng-container matColumnDef="pickup_location">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>
                <td mat-cell *matCellDef="let ride">{{ ride.pickup_location }}</td>
              </ng-container>

              <ng-container matColumnDef="dropoff_location">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>
                <td mat-cell *matCellDef="let ride">{{ ride.dropoff_location }}</td>
              </ng-container>

              <ng-container matColumnDef="pickup_time">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>
                <td mat-cell *matCellDef="let ride">{{ ride.pickup_time | date:'short' }}</td>
              </ng-container>

              <ng-container matColumnDef="fare">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>
                <td mat-cell *matCellDef="let ride">{{ ride.fare ? ( ride.fare * .7 | currency:'USD') : 'TBD' }}</td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let ride">
                  <button mat-raised-button color="primary" (click)="acceptRide(ride.id)">
                    Accept
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="availableColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: availableColumns;"></tr>
            </table>
            <mat-paginator #availablePaginator [pageSizeOptions]="[3,5, 10, 25, 100]" [pageSize]="3" showFirstLastButtons aria-label="Select page of available rides"></mat-paginator>
          </div>
        </mat-tab>

        <mat-tab label="My Rides" [tabIndex]="1" [disabled]="myRides().length === 0">
          <div class="table-container">
            <div class="header-with-actions">
              <h3>My Assigned Rides <span class="realtime-indicator" title="Realtime updates active">●</span></h3>
              <button mat-icon-button color="primary" (click)="loadRides()" matTooltip="Manual refresh">
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
            <div *ngIf="loading" class="loading-container">
              <mat-spinner diameter="40"></mat-spinner>
              <p>Loading rides...</p>
            </div>
            <div *ngIf="!loading && myRides().length === 0" class="no-rides">
              <p>You don't have any assigned rides.</p>
            </div>

            <table mat-table [dataSource]="myRidesDataSource" matSort #myRidesSort="matSort" class="ride-table" *ngIf="!loading && myRides().length > 0">
              <ng-container matColumnDef="pickup_location">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>
                <td mat-cell *matCellDef="let ride">{{ ride.pickup_location }}</td>
              </ng-container>

              <ng-container matColumnDef="dropoff_location">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>
                <td mat-cell *matCellDef="let ride">{{ ride.dropoff_location }}</td>
              </ng-container>

              <ng-container matColumnDef="pickup_time">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>
                <td mat-cell *matCellDef="let ride">{{ ride.pickup_time | date:'short' }}</td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                <td mat-cell *matCellDef="let ride">
                  <span class="status-chip" [ngClass]="'status-' + ride.status">
                    {{ ride.status }}
                  </span>
                </td>
              </ng-container>

              <ng-container matColumnDef="fare">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>
                <td mat-cell *matCellDef="let ride">{{ ride.fare ? (ride.fare * .7 | currency:'USD') : 'TBD' }}</td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let ride">
                  <button mat-raised-button color="primary" *ngIf="ride.status === 'assigned'" (click)="startRide(ride.id)">
                    Start Ride
                  </button>
                  <button mat-raised-button color="accent" *ngIf="ride.status === 'in-progress'" (click)="completeRide(ride.id)">
                    Complete
                  </button>
                  <button mat-icon-button color="primary" *ngIf="ride.status !== 'completed'" (click)="showNavigation(ride)">
                    <mat-icon>navigation</mat-icon>
                  </button>
                  <!-- <button mat-icon-button color="primary" (click)="openChat(ride.id)" matTooltip="Message Rider">
                    <mat-icon>chat</mat-icon>
                  </button> -->
                  <button mat-icon-button color="primary" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                    <mat-icon>visibility</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="myRidesColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: myRidesColumns;"></tr>
            </table>
            <mat-paginator #myRidesPaginator [pageSizeOptions]="[5, 10, 25, 100]" [pageSize]="5" showFirstLastButtons aria-label="Select page of my rides"></mat-paginator>
          </div>
        </mat-tab>
      </mat-tab-group>

      <app-ride-navigation
        *ngIf="selectedRide"
        [ride]="selectedRide"
        (close)="selectedRide = null">
      </app-ride-navigation>

      <div *ngIf="selectedRideId" class="ride-detail-overlay">
        <app-ride-detail
          [rideId]="selectedRideId"
          [onClose]="closeRideDetails.bind(this)"
          (rideUpdated)="onRideUpdated($event)">
        </app-ride-detail>
      </div>
    </div>
  `,
  styles: [`
    .assignments-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .table-container {
      margin: 20px;
    }

    .ride-table {
      width: 100%;
    }

    
    .no-rides {
      padding: 20px;
      text-align: center;
      color: #666;
      font-style: italic;
    }

    .status-chip {
      border-radius: 16px;
      padding: 4px 12px;
      color: white;
      font-weight: 500;
    }

    .status-requested {
      background-color: #ff9800;
    }

    .status-assigned {
      background-color: #2196f3;
    }

    .status-in-progress {
      background-color: #673ab7;
    }

    .status-completed {
      background-color: #4caf50;
    }

    .status-canceled {
      background-color: #f44336;
    }

    .header-with-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #666;
    }

    .loading-container p {
      margin-top: 10px;
    }

    .ride-detail-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .realtime-indicator {
      color: #4caf50;
      font-size: 12px;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
  `]
})
export class RideAssignmentsComponent implements OnInit, OnDestroy, AfterViewInit {

  currentUser: User | null = null;
  selectedRide: Ride | null = null;
  selectedRideId: string | null = null;
  loading: boolean = false;
  private ridesSubscription: Subscription | null = null;

  availableRides = signal<Ride[]>([]);
  myRides = signal<Ride[]>([]);
  // FARE is already in table html just add it here
  availableColumns: string[] = ['pickup_location', 'dropoff_location', 'pickup_time', 'fare', 'actions'];
  myRidesColumns: string[] = ['pickup_location', 'dropoff_location', 'pickup_time', 'status', 'fare', 'actions'];

  availableDataSource = new MatTableDataSource<Ride>([]);
  myRidesDataSource = new MatTableDataSource<Ride>([]);

  @ViewChild('availableSort') availableSort!: MatSort;
  @ViewChild('availablePaginator') availablePaginator!: MatPaginator;
  @ViewChild('myRidesSort') myRidesSort!: MatSort;
  @ViewChild('myRidesPaginator') myRidesPaginator!: MatPaginator;

  constructor(
    private rideService: RideService,
    private authService: AuthService,
    private messageService: MessageService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  async ngOnInit() {
    try {
      await this.loadCurrentUser();
      if (this.currentUser) {
        // Set up realtime subscription to rides
        this.setupRealtimeSubscription();
        // Initial load of rides
        await this.loadRides();
        console.log('✅ Driver dashboard initialized with realtime updates');
      }
    } catch (error) {
      console.error('❌ Error loading current user:', error);
      this.snackBar.open('Failed to load user information', 'Close', { duration: 3000 });
    }
  }

  ngAfterViewInit() {
    // Setup data sources after view is initialized
    setTimeout(() => {
      this.setupDataSources();
    }, 0);
  }

  ngOnDestroy(): void {
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }
  }




  private async loadCurrentUser(): Promise<void> {
    try {
      this.loading = true;
      this.currentUser = await this.authService.getCurrentUser();
      // if (this.currentUser) {
      //   await this.initializeRides();
      // } else {
      //   throw new Error('User not authenticated');
      // }
    } catch (error) {
      console.error('Error loading current user:', error);
      this.snackBar.open('Failed to load user information', 'Close', { duration: 3000 });
    } finally {
      //this.setupPaginators();
      this.loading = false;

    }
  }

  private setupRealtimeSubscription(): void {
    if (!this.currentUser) return;

    console.log('🔄 Setting up realtime subscription for driver:', this.currentUser.id);

    // Subscribe to the RideService's realtime observable
    this.ridesSubscription = this.rideService.rides$.subscribe((allRides: Ride[]) => {
      if (allRides && allRides.length >= 0) {
        // Filter available rides (status = 'requested')
        const available = allRides.filter(ride => ride.status === 'requested');

        // Filter driver's rides (assigned to current user)
        const myRides = allRides.filter(ride =>
          ride.driver_id === this.currentUser?.id &&
          ['assigned', 'in-progress', 'completed'].includes(ride.status)
        );

        // Update signals
        this.availableRides.set(available);
        this.myRides.set(myRides);

        // Update data sources
        this.availableDataSource.data = available;
        this.myRidesDataSource.data = myRides;

        console.log('🔄 Realtime update - Available rides:', available.length, 'My rides:', myRides.length);

        // Show a brief notification when new rides become available
        const currentAvailableCount = this.availableDataSource.data.length;
        if (available.length > currentAvailableCount && currentAvailableCount > 0) {
          this.snackBar.open(`${available.length - currentAvailableCount} new ride(s) available!`, 'View', {
            duration: 5000,
            panelClass: ['success-snackbar']
          });
        }
      }
    });
  }
  private setupDataSources() {
    console.log('Setting up data sources...');
    console.log('Available sort:', this.availableSort);
    console.log('Available paginator:', this.availablePaginator);
    console.log('My rides sort:', this.myRidesSort);
    console.log('My rides paginator:', this.myRidesPaginator);

    // Connect available rides data source to sort and paginator
    if (this.availableSort) {
      this.availableDataSource.sort = this.availableSort;
      console.log('Connected available rides sort');
    }
    if (this.availablePaginator) {
      this.availableDataSource.paginator = this.availablePaginator;
      console.log('Connected available rides paginator');
    }

    // Connect my rides data source to sort and paginator
    if (this.myRidesSort) {
      this.myRidesDataSource.sort = this.myRidesSort;
      console.log('Connected my rides sort');
    }
    if (this.myRidesPaginator) {
      this.myRidesDataSource.paginator = this.myRidesPaginator;
      console.log('Connected my rides paginator');
    }

    // Custom sort function for available rides
    this.availableDataSource.sortingDataAccessor = (item: Ride, property: string) => {
      console.log('Sorting available rides by:', property, 'for item:', item);
      switch (property) {
        case 'pickup_time':
          return item[property] ? new Date(item[property]).getTime() : 0;
        case 'fare':
          return item.fare || 0;
        case 'pickup_location':
        case 'dropoff_location':
          return (item as any)[property]?.toLowerCase() || '';
        default:
          return (item as any)[property] || '';
      }
    };

    // Custom sort function for my rides
    this.myRidesDataSource.sortingDataAccessor = (item: Ride, property: string) => {
      console.log('Sorting my rides by:', property, 'for item:', item);
      switch (property) {
        case 'pickup_time':
          return item[property] ? new Date(item[property]).getTime() : 0;
        case 'fare':
          return item.fare || 0;
        case 'pickup_location':
        case 'dropoff_location':
        case 'status':
          return (item as any)[property]?.toLowerCase() || '';
        default:
          return (item as any)[property] || '';
      }
    };

    console.log("Data sources setup complete");
  }


  async loadRides(): Promise<void> {
    if (!this.currentUser) return;
    console.log('🔄 Loading rides...');
    this.loading = true;
    try {
      // Force refresh from the service (which will trigger realtime updates)
      await this.rideService.getAllRides();

      // The realtime subscription will automatically update the UI
      // But we can also manually load for immediate feedback
      const [available, assigned] = await Promise.all([
        this.rideService.getAvailableRides(),
        this.rideService.getDriverRides(this.currentUser.id)
      ]);

      // Update signals
      this.availableRides.set(available);
      this.myRides.set(assigned);

      // Update data sources
      this.availableDataSource.data = available;
      this.myRidesDataSource.data = assigned;

      // Ensure data sources are properly connected after data is loaded
      setTimeout(() => {
        this.setupDataSources();
      }, 0);

      console.log('✅ Available Rides:', available.length);
      console.log('✅ My Rides:', assigned.length);

      this.snackBar.open('Rides refreshed', 'Close', { duration: 2000 });
    } catch (error: any) {
      console.error('❌ Error loading rides:', error);
      this.snackBar.open(error.message || 'Failed to load rides', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  async acceptRide(rideId: string): Promise<void> {
    if (!this.currentUser) return;

    try {
      await this.rideService.acceptRide(rideId, this.currentUser.id);
      this.snackBar.open('Ride accepted successfully', 'Close', { duration: 3000 });
      // No need to manually refresh - realtime subscription will handle updates
    } catch (error: any) {
      console.error('Error accepting ride:', error);
      this.snackBar.open(error.message || 'Failed to accept ride', 'Close', { duration: 3000 });
    }
  }

  async startRide(rideId: string): Promise<void> {
    try {
      await this.rideService.startRide(rideId);
      this.snackBar.open('Ride started successfully', 'Close', { duration: 3000 });
      // No need to manually refresh - realtime subscription will handle updates
    } catch (error: any) {
      console.error('Error starting ride:', error);
      this.snackBar.open(error.message || 'Failed to start ride', 'Close', { duration: 3000 });
    }
  }

  async completeRide(rideId: string): Promise<void> {
    try {
      await this.rideService.completeRide(rideId);
      this.snackBar.open('Ride completed successfully', 'Close', { duration: 3000 });
      // No need to manually refresh - realtime subscription will handle updates
    } catch (error: any) {
      console.error('Error completing ride:', error);
      this.snackBar.open(error.message || 'Failed to complete ride', 'Close', { duration: 3000 });
    }
  }

  showNavigation(ride: Ride): void {
    this.selectedRide = ride;
  }

  closeNavigation(): void {
    this.selectedRide = null;
  }

  async openChat(rideId: string) {
    try {
      const thread = await this.messageService.getOrCreateThreadForRide(rideId);
      await this.router.navigate(['/dashboard', 'driver', 'messages', thread.id]);
    } catch (error: any) {
      console.error('Error opening chat:', error);
      this.snackBar.open(error.message || 'Failed to open chat', 'Close', { duration: 3000 });
    }
  }

  viewRideDetails(rideId: string) {
    this.selectedRideId = rideId;
  }

  closeRideDetails() {
    this.selectedRideId = null;
    // No need to refresh - realtime subscription will handle updates
  }

  onRideUpdated(_ride: Ride) {
    // No need to reload rides - realtime subscription will handle updates automatically
  }

  trackByRideId(index: number, item: Ride): string {
    return item.id;
  }
}
